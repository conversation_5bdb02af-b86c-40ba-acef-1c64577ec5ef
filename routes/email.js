const express = require('express');
const nodemailer = require('nodemailer');
const User = require('../models/User');

const router = express.Router();

// Create email transporter
const createTransporter = () => {
  return nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    secure: false, // true for 465, false for other ports
    requireTLS: true, // Use TLS encryption
    auth: {
      user: process.env.EMAIL_USER,
      pass: "noreplyPwd#123"
    }
  });
};

// Send email with certificate
const sendCertificateEmail = async (user) => {
  try {
    const transporter = createTransporter();

    const mailOptions = {
      from: `"Alembic Digital Labs" <${process.env.EMAIL_FROM_ADDRESS || process.env.EMAIL_USER}>`,
      to: user.email,
      subject: 'Your Certificate is Ready!',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2c3e50;">Congratulations ${user.name}!</h2>
          <p>We are pleased to inform you that your certificate has been generated successfully.</p>
          <p>Please find your certificate attached to this email.</p>
          <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
            <h3 style="color: #495057; margin-top: 0;">Certificate Details:</h3>
            <p><strong>Name:</strong> ${user.name}</p>
            <p><strong>Type:</strong> ${user.topic}</p>
            <p><strong>Certificate ID:</strong> ${user.uuid}</p>
          </div>
          <p>Thank you for your participation!</p>
          <hr style="border: none; border-top: 1px solid #dee2e6; margin: 30px 0;">
          <p style="color: #6c757d; font-size: 12px;">
            This is an automated email. Please do not reply to this message.
          </p>
        </div>
      `,
      attachments: user.certificateImage ? [
        {
          filename: `Certificate_${user.name.replace(/\s+/g, '_')}.jpg`,
          content: Buffer.from(user.certificateImage),
          encoding: 'base64',
          contentType: 'image/jpeg'
        }
      ] : []
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Email sent successfully:', info.messageId);
    return true;
  } catch (error) {
    console.error('Email sending error:', error);
    throw error;
  }
};

// Send email to individual user
router.post('/send/:userId', async (req, res) => {
  try {
    const user = await User.findByPk(req.params.userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    if (!user.email) {
      return res.status(400).json({
        success: false,
        message: 'Cannot send certificate - user has no email address'
      });
    }

    if (user.watchTime < 20) {
      return res.status(400).json({
        success: false,
        message: 'Cannot send certificate - user has not watched video for sufficient time',
        watchTime: user.watchTime
      });
    }

    if (!user.certificateImage) {
      return res.status(400).json({
        success: false,
        message: 'Certificate not generated yet. Please generate certificate first.'
      });
    }

    await sendCertificateEmail(user);

    // Update user status to completed
    await user.update({ status: 'completed' });

    res.json({
      success: true,
      message: 'Certificate email sent successfully',
      data: {
        userId: user.id,
        email: user.email,
        status: 'completed'
      }
    });

  } catch (error) {
    console.error('Error sending email:', error);
    res.status(500).json({
      success: false,
      message: 'Error sending email',
      error: error.message
    });
  }
});

// Send emails to all pending users
router.post('/send-all', async (req, res) => {
  try {
    const { minDuration = 20 } = req.body;

    const users = await User.findAll({
      where: {
        status: 'pending'
      }
    });

    const results = {
      sent: [],
      skipped: [],
      errors: []
    };

    for (const user of users) {
      try {
        // Check if user is eligible
        if (!user.email) {
          results.skipped.push({
            userId: user.id,
            name: user.name,
            email: 'No email',
            reason: 'No email address'
          });
          continue;
        }

        if (user.watchTime < minDuration) {
          results.skipped.push({
            userId: user.id,
            name: user.name,
            email: user.email,
            reason: `Insufficient watch time (${user.watchTime}s < ${minDuration}s)`,
            watchTime: user.watchTime
          });
          continue;
        }

        if (!user.certificateImage) {
          results.skipped.push({
            userId: user.id,
            name: user.name,
            email: user.email,
            reason: 'Certificate not generated'
          });
          continue;
        }

        // Send email using DB image
        await sendCertificateEmail(user);

        // Update status
        await user.update({ status: 'completed' });

        results.sent.push({
          userId: user.id,
          name: user.name,
          email: user.email
        });

      } catch (error) {
        results.errors.push({
          userId: user.id,
          name: user.name,
          email: user.email,
          error: error.message
        });
      }
    }

    res.json({
      success: true,
      message: 'Bulk email sending completed',
      data: results
    });

  } catch (error) {
    console.error('Error in bulk email sending:', error);
    res.status(500).json({
      success: false,
      message: 'Error in bulk email sending',
      error: error.message
    });
  }
});

// Test email configuration
router.post('/test', async (req, res) => {
  try {
    const transporter = createTransporter();

    // Verify connection
    await transporter.verify();

    res.json({
      success: true,
      message: 'Email configuration is valid'
    });

  } catch (error) {
    console.error('Email configuration test failed:', error);
    res.status(500).json({
      success: false,
      message: 'Email configuration test failed',
      error: error.message
    });
  }
});

module.exports = router;
