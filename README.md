# Certificate Generator Admin Panel

A comprehensive admin panel for managing certificate generation and delivery based on uploaded user data. Built with Node.js, Express, Sequelize, and modern web technologies.

## Features

- **File Upload**: Support for CSV and Excel (.xlsx, .xls) file uploads
- **User Management**: View, manage, and track user data
- **Certificate Generation**: Dynamic certificate creation with user names and QR codes
- **Email Integration**: Send certificates via email with automatic status updates
- **Responsive UI**: Modern, mobile-friendly admin interface
- **Watch Time Validation**: Certificates only generated for users with ≥20 seconds watch time

## Tech Stack

- **Backend**: Node.js, Express.js
- **Database**: SQLite (with Sequelize ORM)
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Image Processing**: Jimp
- **Email**: Nodemailer
- **File Processing**: ExcelJS, CSV-Parser

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Certificate_Genrator_Zoom
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**
   Update the `.env` file with your email configuration:
   ```env
   # Email Configuration
   EMAIL_HOST=smtp.gmail.com
   EMAIL_PORT=587
   EMAIL_USER=<EMAIL>
   EMAIL_PASSWORD=your-app-password
   
   # Server Configuration
   PORT=3000
   NODE_ENV=development
   ```

4. **Start the server**
   ```bash
   npm start
   ```

5. **Access the admin panel**
   Open your browser and navigate to `http://localhost:3000`

## Usage

### 1. Upload Data

1. Navigate to the "Upload Data" tab
2. Upload a CSV or Excel file with the following columns:
   - `name` - Full name of the participant
   - `email` - Email address
   - `type` - Certificate type (optional, defaults to 'Topic')
   - `watchTime` - Video watch time in seconds (optional, random if not provided)

### 2. Manage Users

1. Switch to the "Manage Users" tab
2. View all uploaded users with their status and details
3. Use the action buttons to:
   - **Preview Certificate**: View generated certificate (only for eligible users)
   - **Send Email**: Send certificate via email
   - **Delete**: Remove user from database

### 3. Bulk Operations

- **Generate All Certificates**: Create certificates for all eligible users
- **Send All Emails**: Send certificates to all pending users
- **Refresh**: Reload user data

## File Format Requirements

### CSV Format
```csv
name,email,type,watchTime
John Doe,<EMAIL>,Topic,45
Jane Smith,<EMAIL>,Topic,25
```

### Excel Format
| name | email | type | watchTime |
|------|-------|------|-----------|
| John Doe | <EMAIL> | Topic | 45 |
| Jane Smith | <EMAIL> | Topic | 25 |

## Certificate Generation

- Certificates are only generated for users with watch time ≥ 20 seconds
- Each certificate includes:
  - User's name dynamically added to the template
  - QR code containing the user's UUID
  - Professional styling and layout

## Email Configuration

For Gmail, you'll need to:
1. Enable 2-factor authentication
2. Generate an app-specific password
3. Use the app password in the `EMAIL_PASSWORD` field

## API Endpoints

### Upload
- `POST /api/upload` - Upload CSV/Excel file

### Users
- `GET /api/users` - Get all users
- `GET /api/users/:id` - Get specific user
- `PUT /api/users/:id` - Update user
- `DELETE /api/users/:id` - Delete user

### Certificates
- `POST /api/certificates/generate/:userId` - Generate certificate for user
- `POST /api/certificates/generate-all` - Generate all certificates
- `GET /api/certificates/preview/:userId` - Preview certificate

### Email
- `POST /api/email/send/:userId` - Send email to user
- `POST /api/email/send-all` - Send emails to all pending users
- `POST /api/email/test` - Test email configuration

## Testing

A sample CSV file (`sample_data.csv`) is included for testing purposes. It contains 10 sample users with varying watch times to test the certificate generation logic.

## Project Structure

```
├── config/
│   └── database.js          # Database configuration
├── models/
│   └── User.js              # User model
├── routes/
│   ├── upload.js            # File upload routes
│   ├── users.js             # User management routes
│   ├── certificates.js      # Certificate generation routes
│   └── email.js             # Email sending routes
├── public/
│   ├── css/
│   │   └── style.css        # Styles
│   ├── js/
│   │   └── script.js        # Frontend JavaScript
│   └── index.html           # Admin panel UI
├── Certificates/
│   └── Topic/
│       └── Certificate.jpg  # Certificate template
├── uploads/                 # Temporary file uploads
├── generated_certificates/  # Generated certificates
├── sample_data.csv         # Sample test data
├── server.js               # Main server file
└── package.json            # Dependencies
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the ISC License.
